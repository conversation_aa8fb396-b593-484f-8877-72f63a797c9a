<Window x:Class="DriverManagementSystem.Views.SimpleAssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="1122"
        Width="794"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
>

    <Grid Margin="0,0,0,-888">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Official Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#2C3E50" BorderThickness="0,0,0,3" Padding="40,25">
            <StackPanel HorizontalAlignment="Left" Margin="32,0,0,0">
                <TextBlock Text="الجمهورية اليمنية" FontSize="22" FontWeight="Bold"
                         Foreground="#2C3E50" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                <TextBlock Text="رئاسة مجلس الوزراء" FontSize="18" FontWeight="SemiBold"
                         Foreground="#34495E" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="18" FontWeight="SemiBold"
                         Foreground="#34495E" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                <TextBlock Text="فرع ذمار والبيضاء" FontSize="16" FontWeight="Medium"
                         Foreground="#7F8C8D" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Header -->
        <Border Grid.Row="1" Background="#2C3E50" Padding="40,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📋" FontSize="28" Margin="0,0,15,0" VerticalAlignment="Center"/>
                <TextBlock Text="التكليف الرسمي"
                         FontSize="26" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text=" - زيارة ميدانية"
                         FontSize="18" Foreground="#BDC3C7" VerticalAlignment="Center" Margin="15,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="40,20">
            <Border Background="White" BorderBrush="#E1E8ED" BorderThickness="0"
                    CornerRadius="0" Padding="40">
                <StackPanel>
                    
                    <!-- عنوان التكليف -->
                    <Border Background="#1e3c72" CornerRadius="8" Padding="20" Margin="0,0,0,30">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📋 تكليف رسمي 📋" FontSize="24" FontWeight="Bold"
                                     Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding AssignmentNumber}" FontSize="14"
                                     Foreground="#E8F4FD" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- معلومات الزيارة -->
                    <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                            CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📋 معلومات المهمة" FontSize="16" FontWeight="Bold"
                                     Foreground="#2C3E50" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1" Margin="0,0,0,10">
                                <TextBlock Text="🏗️ المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="مشروع تطوير المجتمعات الريفية" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Margin="0,0,0,10">
                                <TextBlock Text="⚡ النشاط:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding MissionPurpose}" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3">
                                <TextBlock Text="📅 تاريخ التحرك:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock>
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="من {0} إلى {1} ({2})">
                                            <Binding Path="DepartureDate"/>
                                            <Binding Path="ReturnDate"/>
                                            <Binding Path="DurationText"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- القائمون بالزيارة -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="2"
                            CornerRadius="8" Margin="0,0,0,20">
                        <StackPanel>
                            <Border Background="#1e3c72" CornerRadius="6,6,0,0" Padding="15">
                                <TextBlock Text="👥 القائمون بالزيارة" FontSize="16" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <ItemsControl ItemsSource="{Binding Visitors}" Margin="15">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                                                CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="40"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="120"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0" Background="#1e3c72" CornerRadius="20"
                                                        Width="30" Height="30">
                                                    <TextBlock Text="{Binding Index}" FontSize="12" FontWeight="Bold"
                                                             Foreground="White" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"/>
                                                </Border>

                                                <StackPanel Grid.Column="1" Margin="10,0">
                                                    <TextBlock Text="{Binding OfficerName}" FontSize="14" FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding OfficerRank}" FontSize="12" Foreground="#666"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2">
                                                    <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                                    <TextBlock Text="{Binding PhoneNumber}" FontSize="12"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="3">
                                                    <TextBlock Text="🆔 البطاقة" FontSize="10" Foreground="#666"/>
                                                    <TextBlock Text="{Binding OfficerCode}" FontSize="12"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- السائق -->
                    <Border Background="White" BorderBrush="#FF9800" BorderThickness="2"
                            CornerRadius="8" Margin="0,0,0,20">
                        <StackPanel>
                            <Border Background="#FF9800" CornerRadius="6,6,0,0" Padding="15">
                                <TextBlock Text="🚗 السائق المكلف" FontSize="16" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <Border Background="#FFF3E0" Padding="15" Margin="15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="👤 اسم السائق" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding DriverName}" FontSize="14" FontWeight="Bold"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding DriverPhoneNumber}" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="🚙 نوع السيارة" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="{Binding VehicleType}" FontSize="12" FontWeight="Bold"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- النص الختامي -->
                    <Border Background="#F8F9FA" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="وعليه تكرموا بالتعاون مع المذكورين أعلاه لما فيه المصلحة العامة وتحقيق أهداف المؤسسة."
                                     FontSize="14" TextAlignment="Justify" TextWrapping="Wrap" Margin="0,0,0,10"/>
                            <TextBlock Text="وشكراً لحسن تعاونكم" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Right"/>
                        </StackPanel>
                    </Border>

                    <!-- التوقيع -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" HorizontalAlignment="Center">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✍️ التوقيع" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Text="مدير الفرع" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                            <Rectangle Height="1" Width="120" Fill="#1e3c72" Margin="0,10,0,10"/>
                            <TextBlock Text="م/محمد محمد الديلمي" FontSize="12"
                                     Foreground="#2a5298" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Official Footer -->
        <Border Grid.Row="3" Background="White" BorderBrush="#2C3E50" BorderThickness="0,3,0,0" Padding="40,25">
            <TextBlock Text="الجمهورية اليمنية" FontSize="20" FontWeight="Bold"
                     Foreground="#2C3E50" HorizontalAlignment="Center"/>
        </Border>

        <!-- Action Footer -->
        <Border Grid.Row="4" Background="White" BorderBrush="#E1E8ED" BorderThickness="0,1,0,0" Padding="40,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🖨️ طباعة"
                        Click="PrintButton_Click"
                        Background="#28A745" Foreground="White"
                        Padding="30,15" FontWeight="Bold" FontSize="16"
                        BorderThickness="0" Margin="0,0,20,0" Cursor="Hand"/>

                <Button Content="❌ إغلاق"
                        Click="CloseButton_Click"
                        Background="#DC3545" Foreground="White"
                        Padding="30,15" FontWeight="Bold" FontSize="16"
                        BorderThickness="0" Cursor="Hand"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
