using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for SimpleAssignmentWindow.xaml
    /// </summary>
    public partial class SimpleAssignmentWindow : Window
    {
        private readonly FieldVisit _selectedVisit;
        private readonly SimpleAssignmentViewModel _viewModel;

        public SimpleAssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;

            // إنشاء وربط ViewModel
            _viewModel = new SimpleAssignmentViewModel(selectedVisit);
            DataContext = _viewModel;

            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit?.VisitNumber ?? "001"}";
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(this, $"تكليف - زيارة {_selectedVisit?.VisitNumber ?? "001"}");
                    MessageBox.Show("تم إرسال التكليف للطباعة بنجاح", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التكليف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
